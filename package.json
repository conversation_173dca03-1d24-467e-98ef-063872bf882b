{"name": "autodev-work", "private": true, "scripts": {"dev": "pnpm --filter web dev", "build": "pnpm --filter web build", "start": "pnpm --filter web start", "lint": "pnpm --filter web lint", "publish-all:confirm": "nx release", "publish-all": "nx release --dry-run"}, "devDependencies": {"@nx/eslint": "21.0.3", "@nx/jest": "21.0.3", "@nx/next": "21.0.3", "@nx/rollup": "21.0.3", "@nx/vite": "21.0.3", "@nx/web": "21.0.3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitest/ui": "^1.3.1", "eslint": "~8.57.0", "jest": "^29.7.0", "jiti": "2.4.2", "nx": "21.0.3", "typescript": "^5", "vite": "^6.0.0", "vitest": "^1.3.1"}, "dependencies": {"next": "~15.2.4", "react": "19.0.0", "react-dom": "19.0.0"}}