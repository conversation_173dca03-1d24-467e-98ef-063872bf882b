{"$schema": "./node_modules/nx/schemas/nx-schema.json", "defaultBase": "master", "release": {"changelog": {"workspaceChangelog": {"createRelease": "github"}}}, "plugins": [{"plugin": "@nx/eslint/plugin", "options": {"targetName": "eslint:lint"}}, {"plugin": "@nx/jest/plugin", "options": {"targetName": "jest:test"}}, {"plugin": "@nx/vite/plugin", "options": {"buildTargetName": "build", "testTargetName": "vite:test", "serveTargetName": "serve", "devTargetName": "dev", "previewTargetName": "preview", "serveStaticTargetName": "serve-static", "typecheckTargetName": "typecheck", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"plugin": "@nx/next/plugin", "options": {"startTargetName": "next:start", "buildTargetName": "next:build", "devTargetName": "next:dev", "serveStaticTargetName": "serve-static", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}], "neverConnectToCloud": true}