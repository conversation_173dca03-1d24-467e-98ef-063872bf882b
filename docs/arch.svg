<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200px" height="602px" viewBox="-100 -80 802 682" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <title>PlatformEngFlow</title>
    <desc>Platform Engineering Flow Diagram</desc>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(1.000000, 1.000000)">
            <g transform="translate(0.000000, -65.000000)">
                <rect stroke="#1E90FF" stroke-width="2" fill="#FFFFFF" x="-275" y="0" width="1060" height="120" rx="8"/>
                <text fill="#1E90FF" font-family="PingFangSC-Semibold, PingFang SC" font-size="18" font-weight="500">
                    <tspan x="230" y="31">Workbench：开发者智能工作台</tspan>
                </text>
                <text fill="#000000" font-family="PingFangSC-Regular, PingFang SC" font-size="14">
                    <tspan x="230" y="70">- 统一的上下文与研发环境</tspan>
                </text>
                <text fill="#000000" font-family="PingFangSC-Regular, PingFang SC" font-size="14">
                    <tspan x="230" y="95">- AI 自动化开发</tspan>
                </text>
            </g>

            <!-- 箭头 0 (从顶层到用户触点) -->
            <g transform="translate(260.000000, 65.000000)" stroke="#FFD700" stroke-width="2">
                <path d="M10,0 L10,30 M0,20 L10,30 L20,20"/>
            </g>

            <g transform="translate(0.000000, 115.000000)">
                <rect stroke="#FFA500" stroke-width="2" fill="#FFFFFF" x="5" y="0" width="500" height="120" rx="8"/>
                <text fill="#FFA500" font-family="PingFangSC-Semibold, PingFang SC" font-size="18" font-weight="500">
                    <tspan x="37" y="31">用户触点：AI 增强的自服务</tspan>
                </text>
                <text fill="#000000" font-family="PingFangSC-Regular, PingFang SC" font-size="14">
                    <tspan x="37" y="70">- 低代码系统，快速 UI/原型构建</tspan>
                </text>
                <text fill="#000000" font-family="PingFangSC-Regular, PingFang SC" font-size="14">
                    <tspan x="37" y="95">- AI 赋能模板应用，终端用户产品</tspan>
                </text>
                <rect stroke="#FFA500" fill-opacity="0.2" fill="#FFA500" x="340" y="62" width="140" height="54" rx="5"/>
                <text fill="#FFA500" font-family="PingFangSC-Regular, PingFang SC" font-size="14">
                    <tspan x="355" y="92">低代码强化生成</tspan>
                </text>
                <rect stroke="#800080" fill-opacity="0.2" fill="#800080" x="340" y="4" width="140" height="54" rx="5"/>
                <text fill="#800080" font-family="PingFangSC-Regular, PingFang SC" font-size="14">
                    <tspan x="355" y="36">AI 增强模板生成</tspan>
                </text>
            </g>

            <!-- 箭头 1 -->
            <g transform="translate(260.000000, 245.000000)" stroke="#FFD700" stroke-width="2">
                <path d="M10,0 L10,30 M0,20 L10,30 L20,20"/>
            </g>

            <!-- 中间层 -->
            <g transform="translate(0.000000, 295.000000)">
                <rect stroke="#008000" stroke-width="2" fill="#FFFFFF" x="5" y="0" width="500" height="120" rx="8"/>
                <text fill="#008000" font-family="PingFangSC-Semibold, PingFang SC" font-size="18" font-weight="500">
                    <tspan x="29" y="34">中间层：知识与上下文中枢</tspan>
                </text>
                <text fill="#000000" font-family="PingFangSC-Regular, PingFang SC" font-size="14">
                    <tspan x="32" y="74">- 连接用户和内部开发者，聚合知识与分发知识</tspan>
                </text>
                <text fill="#000000" font-family="PingFangSC-Regular, PingFang SC" font-size="14">
                    <tspan x="32" y="99">- 支持问答、DevOps 任务协同</tspan>
                </text>
                <rect stroke="#008000" fill-opacity="0.2" fill="#008000" x="340" y="35" width="140" height="54" rx="5"/>
                <text fill="#008000" font-family="PingFangSC-Regular, PingFang SC" font-size="14">
                    <tspan x="355" y="68">知识与上下文中枢</tspan>
                </text>
            </g>

            <!-- 箭头 2 -->
            <g transform="translate(260.000000, 425.000000)" stroke="#FFD700" stroke-width="2">
                <path d="M10,0 L10,30 M0,20 L10,30 L20,20"/>
            </g>

            <!-- 内部接口 -->
            <g transform="translate(0.000000, 475.000000)">
                <rect stroke="#0000FF" stroke-width="2" fill="#FFFFFF" x="5" y="0" width="500" height="120" rx="8"/>
                <text fill="#0000FF" font-family="PingFangSC-Semibold, PingFang SC" font-size="18" font-weight="500">
                    <tspan x="27" y="26">内部接口：标准化 API 提供上下文</tspan>
                </text>
                <text fill="#000000" font-family="PingFangSC-Regular, PingFang SC" font-size="14">
                    <tspan x="27" y="72">- 提供编辑器、代码库等内部 API</tspan>
                </text>
                <text fill="#000000" font-family="PingFangSC-Regular, PingFang SC" font-size="14">
                    <tspan x="27" y="97">- 平台基础设施与数据处理能力</tspan>
                </text>
                <rect stroke="#0000FF" fill-opacity="0.2" fill="#0000FF" x="340" y="35" width="140" height="54" rx="5"/>
                <text fill="#0000FF" font-family="PingFangSC-Regular, PingFang SC" font-size="14">
                    <tspan x="355" y="68">标准化"智能 API"</tspan>
                </text>
            </g>

            <!-- 智能体治理中枢 -->
            <g transform="translate(-280.000000, 65.000000)">
                <rect stroke="#4B0082" stroke-width="2" fill="#FFFFFF" width="250" height="530" rx="8"/>
                <text fill="#4B0082" font-family="PingFangSC-Semibold, PingFang SC" font-size="18" font-weight="500">
                    <tspan x="57" y="40">智能体治理中枢</tspan>
                </text>
                <line x1="75" y1="50" x2="175" y2="50" stroke="#4B0082" stroke-width="2"/>
                <text fill="#000000" font-family="PingFangSC-Regular, PingFang SC" font-size="14">
                    <tspan x="32" y="92">- 集中治理策略</tspan>
                    <tspan x="32" y="132">- 权限管理</tspan>
                    <tspan x="32" y="172">- 上下文注入</tspan>
                    <tspan x="32" y="212">- 统一审计</tspan>
                    <tspan x="32" y="252">- 资源编排</tspan>
                    <tspan x="32" y="292">- 智能工作流编排</tspan>
                </text>
                <rect stroke="#4B0082" fill-opacity="0.2" fill="#4B0082" x="40" y="450" width="188" height="54" rx="5"/>
                <text fill="#4B0082" font-family="PingFangSC-Regular, PingFang SC" font-size="14">
                    <tspan x="65" y="482">IDP 作为智能治理中枢</tspan>
                </text>
            </g>

            <!-- AI 度量 -->
            <g transform="translate(540.000000, 65.000000)">
                <rect stroke="#2E8B57" stroke-width="2" fill="#FFFFFF" width="250" height="530" rx="8"/>
                <text fill="#2E8B57" font-family="Helvetica-Bold, Helvetica" font-size="18" font-weight="bold">
                    <tspan x="40" y="40">度量 AI 质量与生产力</tspan>
                </text>
                <line x1="30" y1="50" x2="220" y2="50" stroke="#2E8B57" stroke-width="2"/>
                <text fill="#000000" font-family="PingFangSC-Regular, PingFang SC" font-size="14">
                    <tspan x="40" y="92">- 模型效果评估</tspan>
                    <tspan x="40" y="132">- 响应速度与准确率</tspan>
                    <tspan x="40" y="172">- 使用频率与覆盖率</tspan>
                    <tspan x="40" y="212">- 生产效率提升指标</tspan>
                    <tspan x="40" y="252">- 代码质量与测试覆盖率</tspan>
                </text>
                <rect stroke="#2E8B57" fill-opacity="0.2" fill="#2E8B57" x="20" y="450" width="210" height="54" rx="5"/>
                <text fill="#2E8B57" font-family="PingFangSC-Regular, PingFang SC" font-size="14">
                    <tspan x="60" y="482">度量改善 AI 生成质量</tspan>
                </text>
            </g>

            <!-- 左连接线 -->
            <g>
                <line x1="-30" y1="181" x2="10" y2="181" stroke="#4B0082" stroke-width="2" stroke-dasharray="5,5"/>
                <line x1="-30" y1="351" x2="10" y2="351" stroke="#4B0082" stroke-width="2" stroke-dasharray="5,5"/>
                <line x1="-30" y1="531" x2="10" y2="531" stroke="#4B0082" stroke-width="2" stroke-dasharray="5,5"/>
            </g>

            <!-- 右连接线 -->
            <g>
                <line x1="505" y1="181" x2="540" y2="181" stroke="#2E8B57" stroke-width="2" stroke-dasharray="5,5"/>
                <line x1="505" y1="351" x2="540" y2="351" stroke="#2E8B57" stroke-width="2" stroke-dasharray="5,5"/>
                <line x1="505" y1="531" x2="540" y2="531" stroke="#2E8B57" stroke-width="2" stroke-dasharray="5,5"/>
            </g>

            <!-- 从 Workbench 到治理和度量的连接 -->
            <g>
                <line x1="-155" y1="55" x2="-155" y2="65" stroke="#4B0082" stroke-width="2" stroke-dasharray="5,5"/>
                <line x1="665" y1="55" x2="665" y2="65" stroke="#2E8B57" stroke-width="2" stroke-dasharray="5,5"/>
            </g>
        </g>
    </g>
</svg>
