name: Analysis issue with AutoDev Remote Agent

on:
  issues:
    types: [opened, edited, reopened]
  workflow_dispatch:  # 允许手动触发测试

jobs:
  test-ai-agent-analysis:
    runs-on: ubuntu-latest
    if: github.event_name == 'issues'

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install Ripgrep
        run: sudo apt-get update && sudo apt-get install -y ripgrep

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 8

      - name: Install workspace dependencies
        run: pnpm install

      - name: Build packages in correct order
        run: |
          pnpm --filter worker-core build
          pnpm --filter worker-protobuf build
          pnpm --filter context-worker build
          pnpm --filter remote-agent build

      - name: Test GitHub Agent with AI Agent CLI
        timeout-minutes: 10
        run: |
          cd packages/remote-agent
          echo "🤖 Testing AI Agent CLI with GitHub issue analysis"
          echo "📋 Issue: ${{ github.event.issue.number || 'workflow_dispatch' }}"
          echo "🎯 Repository: ${{ github.repository }}"
          echo ""

          # Set timeout and error handling
          set -e
          trap 'echo "❌ Process interrupted or timed out"; exit 1' INT TERM

          # Test AI Agent with issue analysis and comment posting
          if [ "${{ github.event_name }}" = "issues" ]; then
            echo "🔍 Analyzing GitHub issue #${{ github.event.issue.number }} and posting results"
            timeout 480s node bin/agent.js --auto-upload --verbose --workspace "${{ github.workspace }}" \
              --command "Analyze GitHub issue #${{ github.event.issue.number }} in ${{ github.repository }} and post the analysis results as a comment to the issue. Focus on providing detailed insights and actionable recommendations." || {
              echo "⚠️ Analysis timed out or failed, but continuing..."
              exit 0
            }
          else
            echo "🧪 Testing AI Agent capabilities"
            timeout 120s node bin/agent.js --auto-upload --verbose --workspace "${{ github.workspace }}" \
              --command "Show available tools and capabilities" || {
              echo "⚠️ Tool listing timed out or failed, but continuing..."
              exit 0
            }
          fi

          echo "✅ AI Agent test completed successfully"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          DEEPSEEK_TOKEN: ${{ secrets.DEEPSEEK_TOKEN }}
          GLM_TOKEN: ${{ secrets.GLM_TOKEN }}
          NODE_OPTIONS: "--max-old-space-size=4096"
          # GitHub Actions context variables
          GITHUB_ACTIONS: true
          GITHUB_REPOSITORY: ${{ github.repository }}
          GITHUB_EVENT_NAME: ${{ github.event_name }}
          GITHUB_EVENT_PATH: ${{ github.event_path }}

      # New steps for compressing logs and uploading as artifacts
      - name: Compress logs directory
        if: always()  # Run even if previous steps fail
        run: |
          cd packages/remote-agent
          tar -czvf remote-agent-logs.tar.gz logs/

      - name: Upload logs as artifacts
        if: always()  # Run even if previous steps fail
        uses: actions/upload-artifact@v4
        with:
          name: remote-agent-logs
          path: packages/remote-agent/remote-agent-logs.tar.gz
          retention-days: 7
