name: CI/CD Pipeline

on:
  push:
    branches: [ master ]
  pull_request:

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout 🛎️
        uses: actions/checkout@v3

      - name: Install Ripgrep
        run: sudo apt-get update && sudo apt-get install -y ripgrep

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
          run_install: false

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Worker Core Build
        run: pnpm --filter worker-core build

      - name: Web Build
        run: pnpm --filter web build:ci

      - name: ProtobufWorker Build
        run: pnpm --filter worker-protobuf build

      - name: ContextWorker Build
        run: pnpm --filter context-worker build

      - name: GitHub Agent Build
        run: pnpm --filter remote-agent build

      - name: GitHub Agent Action Build
        run: pnpm --filter remote-agent-action build

      - name: Run Core Tests
        run: pnpm --filter worker-core vitest run

      - name: Run Web Tests
        run: pnpm --filter web vitest run

      - name: Run Context Worker Tests
        run: pnpm --filter context-worker test

      - name: Run MCP Tests
        run: pnpm --filter context-mcp vitest run

      - name: Run GitHub Agent Tests
        run: pnpm --filter remote-agent test

      - name: Run GitHub Agent Action Tests
        run: pnpm --filter remote-agent-action test
        continue-on-error: true

