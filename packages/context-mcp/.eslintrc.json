{"parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"], "env": {"node": true, "es6": true}, "parserOptions": {"ecmaVersion": 2020, "sourceType": "module"}, "rules": {"@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}]}}