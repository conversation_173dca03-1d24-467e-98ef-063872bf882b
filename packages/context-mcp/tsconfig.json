{"extends": "../../tsconfig.base.json", "compilerOptions": {"target": "ES2020", "module": "ESNext", "lib": ["ES2020"], "declaration": true, "declarationDir": "./dist/types", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "allowSyntheticDefaultImports": true, "types": ["jest", "node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}