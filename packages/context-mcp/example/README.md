# Example MCP Server

This is a simple example of an MCP (Message Control Protocol) server implementation. The server demonstrates how to:

1. Create and configure an MCP server
2. Start the server with HTTP support

## Running the Example

To run the example server:

```bash
# From the context-mcp directory
pnpm run run-example
```

The server will start on port 3000.

## Server Configuration

The example server is configured with:
- Server name: example-mcp-server
- Version: 1.0.0
- Port: 3000