# MCP Prompts

This is a collection of prompts that can be used to extend the capabilities of the MCP server.

## Code Review Prompts

### Review PR GitHub
- `review-pr-github`: Generates a comprehensive GitHub PR review comment
  - Markdown formatted review
  - Code block syntax highlighting
  - Task lists for actionable items
  - Emoji reactions for different types of feedback
  - Line references
  - User mentions
  - Collapsible sections for long reviews

### Review Code
- `review-code`: Basic code review prompt
  - General code review guidelines
  - Best practices check
  - Code quality assessment

## Usage

Each prompt can be used independently and supports various parameters for customization. The prompts are designed to work with the corresponding tools to provide a complete solution for code review and PR management.
