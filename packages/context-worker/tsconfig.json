{
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "outDir": "./dist",
    "rootDir": "./src",
    "noEmit": false,
    "declaration": true,
    "declarationDir": "./dist/types",
    "importHelpers": true,
    "esModuleInterop": true,
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "types": [
      "node",
      "vite/client",
      "vitest/globals",
      "reflect-metadata"
    ],
    "typeRoots": [
      "./node_modules/@types",
      "./src/types"
    ],
    /* ioc */
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "lib": [
      "ES2022.Error"
    ]
  },
  "include": [
    "src/**/*",
    "test/**/*",
    "vitest/globals.d.ts"
  ],
  "exclude": [
    "node_modules"
  ]
}
