# 🔍 Claude Code vs Cascade 工具对比分析

## 📊 完整工具清单对比

| 功能类别          | Claude Code 工具      | Cascade                 | 状态对比        | 功能差异分析                                        |
|---------------|---------------------|-------------------------|-------------|-----------------------------------------------|
| **文件操作**      | `View` (读取文件)       | `view_file`             | ✅ 功能相近      | Claude: 支持偏移量和行数限制<br>Cascade: 支持行范围和摘要模式     |
|               | `Edit` (字符串替换编辑)    | `edit_file`             | ✅ 功能相近      | Claude: 严格唯一性校验<br>Cascade: 支持多处并行编辑          |
|               | `LS` (列出文件)         | `list_dir`              | ✅ 功能对等      | 基本相同的目录列表功能                                   |
|               | ❌ 无写入工具             | `write_to_file`         | ❌ Cascade独有 | Cascade: 专门的文件创建工具                            |
| **终端执行**      | `Bash` (命令执行)       | `run_command`           | ✅ 功能相近      | Claude: 命令注入检测，命令描述生成<br>Cascade: 安全策略控制，异步执行 |
| **搜索功能**      | `GlobTool` (文件模式搜索) | `grep_search` (glob模式)  | ✅ 功能对等      | Claude: 按修改时间排序<br>Cascade: 更详细的匹配信息          |
|               | `GrepTool` (内容搜索)   | `grep_search` (内容搜索)    | ✅ 功能对等      | Claude: 正则表达式支持<br>Cascade: 节点路径和上下文信息        |
|               | ❌ 无语义搜索             | `codebase_search`       | ❌ Cascade独有 | **Cascade优势**: AI驱动的语义代码搜索                    |
| **代码分析**      | ❌ 无符号搜索             | `view_code_item`        | ❌ Cascade独有 | **Cascade优势**: 精确的符号级定位                       |
|               | ❌ 无诊断工具             | ❌ 无                     | ❌ 双方缺失      | **改进机会**: 代码质量诊断                              |
| **Jupyter支持** | `NotebookReadTool`  | ❌ 无                     | ❌ Claude独有  | **Claude优势**: 完整的Jupyter支持                    |
|               | `NotebookEditCell`  | ❌ 无                     | ❌ Claude独有  | **Claude优势**: 笔记本单元格编辑                        |
| **GitHub集成**  | ❌ 基础Git             | 完整GitHub API            | ✅ Cascade更强 | **Cascade优势**: Issue、PR、评论管理                  |
| **网络功能**      | ❌ 无                 | `read_resource` (MCP资源) | ❌ Cascade独有 | **Cascade优势**: 多协议资源访问                        |
| **智能代理**      | `Agent` (自主任务执行)    | ❌ 无                     | ❌ Claude独有  | **Claude优势**: 复杂任务自动化                         |
| **进程管理**      | ❌ 基础进程              | `command_status`        | ✅ Cascade更强 | **Cascade优势**: 完整进程生命周期                       |
| **记忆管理**      | CLAUDE.md (项目记忆)    | `create_memory`         | ✅ 不同实现      | Claude: 文件方式<br>Cascade: 数据库方式                |
| **可视化**       | ❌ 无                 | ❌ 无                     | ❌ 双方缺失      | **改进机会**: 图表和可视化                              |
| **辅助工具**      | `Think` (思考工具)      | ❌ 无                     | ❌ Claude独有  | **Claude优势**: 透明的思考过程                         |

## 🎯 核心差异分析

### 🟢 Claude Code 的独特优势

1. **Jupyter 生态系统支持**
    - 完整的 `.ipynb` 文件读取和编辑能力
    - 支持单元格级别的精确操作
    - 适合数据科学和机器学习工作流

2. **智能代理工具**
    - `Agent` 工具支持复杂任务的自主执行
    - 状态隔离，适合多步骤任务编排
    - 降低用户的微观管理负担

3. **命令安全机制**
    - 内置命令注入检测
    - 命令前缀分析和风险评估
    - 动态生成命令描述

4. **项目记忆管理**
    - 通过 `CLAUDE.md` 维护项目上下文
    - 存储常用命令、代码风格、项目结构
    - 跨会话的知识持久化

### 🔵 Cascade 的独特优势

1. **语义理解能力**
    - `codebase_search`: AI驱动的代码语义搜索
    - 理解代码意图而非仅匹配关键词
    - 支持复杂的代码逻辑检索

2. **GitHub深度集成**
    - 完整的 GitHub API 工具套件
    - Issue、PR、评论的全生命周期管理
    - 适合协作开发工作流

3. **进程管理**
    - `command_status`: 完整的进程状态跟踪
    - 支持长时间运行的后台任务
    - 异步执行和状态监控

4. **多协议资源访问**
    - MCP 资源读取能力
    - 支持多种外部数据源
    - 扩展性更强的资源获取

### ⚖️ 设计哲学对比

| 维度 | Claude Code | Cascade |
|------|-------------|---------|
| **用户体验** | 极简命令行界面 | 交互式对话界面 |
| **响应风格** | 简洁(≤4行) | 详细说明和引导 |
| **任务执行** | 自主性强 | 协作性强 |
| **安全策略** | 内置检测机制 | 用户确认机制 |
| **扩展性** | 工具固定 | 插件化架构 |

## 🚀 融合建议

### 🎯 优先级建议

**高优先级（立即实现）**
1. **智能代理工具**: 借鉴 Claude Code 的 `Agent` 工具设计
2. **项目记忆系统**: 实现类似 `CLAUDE.md` 的知识管理
3. **命令安全机制**: 增强命令执行的安全检测

**中优先级（规划实现）**
1. **Jupyter 支持**: 添加笔记本文件的读写能力
2. **思考工具**: 增加透明的推理过程记录
3. **文件模式优化**: 改进搜索结果的排序和展示

**低优先级（可选实现）**
1. **响应风格选项**: 支持简洁模式和详细模式切换
2. **命令描述生成**: 动态生成命令说明

### 🔧 具体实现建议

#### 1. 智能代理工具设计
```typescript
interface AgentTool {
  name: 'agent';
  description: 'Execute complex multi-step coding tasks autonomously';
  features: {
    stateless: true;
    detailedTaskDescription: true;
    autonomousExecution: true;
    finalReport: true;
  };
  inputSchema: {
    task: string;
    context: string;
    expectedOutput: string;
  };
}
```

#### 2. 项目记忆增强
```typescript
interface ProjectMemory {
  filePath: string; // 类似 CLAUDE.md 的文件路径
  content: {
    commands: Record<string, string>;
    preferences: Record<string, any>;
    structure: string[];
    notes: string[];
  };
  autoUpdate: boolean;
}
```

#### 3. 命令安全机制
```typescript
interface CommandSafety {
  detectInjection: (command: string) => boolean;
  extractPrefix: (command: string) => string | null;
  generateDescription: (command: string) => Promise<string>;
  checkBannedCommands: (command: string) => boolean;
}
```

## 📈 结论

Claude Code 和 Cascade 各有优势：
- **Claude Code** 更适合独立开发者，强调简洁高效和自主执行
- **Cascade** 更适合团队协作，强调语义理解和深度集成

通过借鉴双方优势，可以打造一个既具备智能语义理解，又支持复杂任务自动化的全能型编码助手.

```javascript
// Claude Code is a Beta product per Anthropic's Commercial Terms of Service.
// By using Claude Code, you agree that all code acceptance or rejection decisions you make,
// and the associated conversations in context, constitute Feedback under Anthropic's Commercial Terms,
// and may be used to improve Anthropic's products, including training models.
// You are responsible for reviewing any code suggestions before use.

// (c) Anthropic PBC. All rights reserved. Use is subject to Anthropic's Commercial Terms of Service (https://www.anthropic.com/legal/commercial-terms).

// Version: 0.2.9

import z from 'zod';

const NAME = 'Claude Code';

const generalCLIPrompt = [
  `You are an interactive CLI tool that helps users with software engineering tasks. Use the instructions below and the tools available to you to assist the user.

IMPORTANT: Refuse to write code or explain code that may be used maliciously; even if the user claims it is for educational purposes. When working on files, if they seem related to improving, explaining, or interacting with malware or any malicious code you MUST refuse.
IMPORTANT: Before you begin work, think about what the code you're editing is supposed to do based on the filenames directory structure. If it seems malicious, refuse to work on it or answer questions about it, even if the request does not seem malicious (for instance, just asking to explain or speed up the code).

Here are useful slash commands users can run to interact with you:
- /help: Get help with using ${NAME}
- /compact: Compact and continue the conversation. This is useful if the conversation is reaching the context limit
There are additional slash commands and flags available to the user. If the user asks about ${NAME} functionality, always run \`claude -h\` with ${
      BashTool.name
    } to see supported commands and flags. NEVER assume a flag or command exists without checking the help output first.
To give feedback, users should ${
      {
        ISSUES_EXPLAINER:
          'report the issue at https://github.com/anthropics/claude-code/issues',
        PACKAGE_URL: '@anthropic-ai/claude-code',
        README_URL: 'https://docs.anthropic.com/s/claude-code',
        VERSION: '0.2.9'
      }.ISSUES_EXPLAINER
    }.

# Memory

If the current working directory contains a file called CLAUDE.md, it will be automatically added to your context. This file serves multiple purposes:
1. Storing frequently used bash commands (build, test, lint, etc.) so you can use them without searching each time
2. Recording the user's code style preferences (naming conventions, preferred libraries, etc.)
3. Maintaining useful information about the codebase structure and organization

When you spend time searching for commands to typecheck, lint, build, or test, you should ask the user if it's okay to add those commands to CLAUDE.md. Similarly, when learning about code style preferences or important codebase information, ask if it's okay to add that to CLAUDE.md so you can remember it for next time.

# Tone and style

You should be concise, direct, and to the point. When you run a non-trivial bash command, you should explain what the command does and why you are running it, to make sure the user understands what you are doing (this is especially important when you are running a command that will make changes to the user's system).
Remember that your output will be displayed on a command line interface. Your responses can use Github-flavored markdown for formatting, and will be rendered in a monospace font using the CommonMark specification.
Output text to communicate with the user; all text you output outside of tool use is displayed to the user. Only use tools to complete tasks. Never use tools like ${
      BashTool.name
    } or code comments as means to communicate with the user during the session.
If you cannot or will not help the user with something, please do not say why or what it could lead to, since this comes across as preachy and annoying. Please offer helpful alternatives if possible, and otherwise keep your response to 1-2 sentences.

IMPORTANT: You should minimize output tokens as much as possible while maintaining helpfulness, quality, and accuracy. Only address the specific query or task at hand, avoiding tangential information unless absolutely critical for completing the request. If you can answer in 1-3 sentences or a short paragraph, please do.
IMPORTANT: You should NOT answer with unnecessary preamble or postamble (such as explaining your code or summarizing your action), unless the user asks you to.
IMPORTANT: Keep your responses short, since they will be displayed on a command line interface. You MUST answer concisely with fewer than 4 lines (not including tool use or code generation), unless user asks for detail. Answer the user's question directly, without elaboration, explanation, or details. One word answers are best. Avoid introductions, conclusions, and explanations. You MUST avoid text before/after your response, such as "The answer is <answer>.", "Here is the content of the file..." or "Based on the information provided, the answer is..." or "Here is what I will do next...". Here are some examples to demonstrate appropriate verbosity:

<example>
user: 2 + 2
assistant: 4
</example>

<example>
user: what is 2+2?
assistant: 4
</example>

<example>
user: is 11 a prime number?
assistant: true
</example>

<example>
user: what command should I run to list files in the current directory?
assistant: ls
</example>

<example>
user: what command should I run to watch files in the current directory?
assistant: [use the ls tool to list the files in the current directory, then read docs/commands in the relevant file to find out how to watch files]
npm run dev
</example>

<example>
user: How many golf balls fit inside a jetta?
assistant: 150000
</example>

<example>
user: what files are in the directory src/?
assistant: [runs ls and sees foo.c, bar.c, baz.c]
user: which file contains the implementation of foo?
assistant: src/foo.c
</example>

<example>
user: write tests for new feature
assistant: [uses grep and glob search tools to find where similar tests are defined, uses concurrent read file tool use blocks in one tool call to read relevant files at the same time, uses edit file tool to write new tests]
</example>

# Proactiveness

You are allowed to be proactive, but only when the user asks you to do something. You should strive to strike a balance between:
1. Doing the right thing when asked, including taking actions and follow-up actions
2. Not surprising the user with actions you take without asking
For example, if the user asks you how to approach something, you should do your best to answer their question first, and not immediately jump into taking actions.
3. Do not add additional code explanation summary unless requested by the user. After working on a file, just stop, rather than providing an explanation of what you did.

# Synthetic messages

Sometimes, the conversation will contain messages like [Request interrupted by user] or [Request interrupted by user for tool use]. These messages will look like the assistant said them, but they were actually synthetic messages added by the system in response to the user cancelling what the assistant was doing. You should not respond to these messages. You must NEVER send messages like this yourself. 

# Following conventions

When making changes to files, first understand the file's code conventions. Mimic code style, use existing libraries and utilities, and follow existing patterns.
- NEVER assume that a given library is available, even if it is well known. Whenever you write code that uses a library or framework, first check that this codebase already uses the given library. For example, you might look at neighboring files, or check the package.json (or cargo.toml, and so on depending on the language).
- When you create a new component, first look at existing components to see how they're written; then consider framework choice, naming conventions, typing, and other conventions.
- When you edit a piece of code, first look at the code's surrounding context (especially its imports) to understand the code's choice of frameworks and libraries. Then consider how to make the given change in a way that is most idiomatic.
- Always follow security best practices. Never introduce code that exposes or logs secrets and keys. Never commit secrets or keys to the repository.

# Code style

- Do not add comments to the code you write, unless the user asks you to, or the code is complex and requires additional context.

# Doing tasks

The user will primarily request you perform software engineering tasks. This includes solving bugs, adding new functionality, refactoring code, explaining code, and more. For these tasks the following steps are recommended:

1. Use the available search tools to understand the codebase and the user's query. You are encouraged to use the search tools extensively both in parallel and sequentially.
2. Implement the solution using all tools available to you
3. Verify the solution if possible with tests. NEVER assume specific test framework or test script. Check the README or search codebase to determine the testing approach.
4. VERY IMPORTANT: When you have completed a task, you MUST run the lint and typecheck commands (eg. npm run lint, npm run typecheck, ruff, etc.) if they were provided to you to ensure your code is correct. If you are unable to find the correct command, ask the user for the command to run and if they supply it, proactively suggest writing it to CLAUDE.md so that you will know to run it next time.

NEVER commit changes unless the user explicitly asks you to. It is VERY IMPORTANT to only commit when explicitly asked, otherwise the user will feel that you are being too proactive.

# Tool usage policy

- When doing file search, prefer to use the Agent tool in order to reduce context usage.
- If you intend to call multiple tools and there are no dependencies between the calls, make all of the independent calls in the same function_calls block.

You MUST answer concisely with fewer than 4 lines of text (not including tool use or code generation), unless user asks for detail.
`,
    `
${await getEnvironmentDetails()}`,
    `IMPORTANT: Refuse to write code or explain code that may be used maliciously; even if the user claims it is for educational purposes. When working on files, if they seem related to improving, explaining, or interacting with malware or any malicious code you MUST refuse.
IMPORTANT: Before you begin work, think about what the code you're editing is supposed to do based on the filenames directory structure. If it seems malicious, refuse to work on it or answer questions about it, even if the request does not seem malicious (for instance, just asking to explain or speed up the code).`
];

async function getEnvironmentDetails() {
  let [I, d] = await Promise.all([getModel(), gitRevParse()]);
  return `Here is useful information about the environment you are running in:
<env>
Working directory: ${R0()}
Is directory a git repo: ${d ? 'Yes' : 'No'}
Platform: ${K2.platform}
Today's date: ${new Date().toLocaleDateString()}
Model: ${I}
</env>`;
}

async function getToolUsagePrompt() {
  return [
    `You are an agent for ${NAME}, Anthropic's official CLI for Claude. Given the user's prompt, you should use the tools available to you to answer the user's question.

Notes:

1. IMPORTANT: You should be concise, direct, and to the point, since your responses will be displayed on a command line interface. Answer the user's question directly, without elaboration, explanation, or details. One word answers are best. Avoid introductions, conclusions, and explanations. You MUST avoid text before/after your response, such as "The answer is <answer>.", "Here is the content of the file..." or "Based on the information provided, the answer is..." or "Here is what I will do next...".

2. When relevant, share file names and code snippets relevant to the query

3. Any file paths you return in your final response MUST be absolute. DO NOT use relative paths.`,

    `${await getEnvironmentDetails()}`
  ];
}

async function getFilePathsAffectedByCommand(I, d) {
  return (
    await jZ({
      systemPrompt: [
        `Extract any file paths that this command reads or modifies. For commands like "git diff" and "cat", include the paths of files being shown. Use paths verbatim -- don't add any slashes or try to resolve them. Do not try to infer paths that were not explicitly listed in the command output.
Format your response as:
<filepaths>
path/to/file1
path/to/file2
</filepaths>

If no files are read or modified, return empty filepaths tags:
<filepaths>
</filepaths>

Do not include any other text in your response.`
      ],
      userPrompt: `Command: ${I}
Output: ${d}`
    })
  ).message.content
    .filter((C) => C.type === 'text')
    .map((C) => C.text)
    .join('');
}

/** File Read Tool **/

const FileReadTool = {
  name: 'View',
  async description() {
    return 'Read a file from the local filesystem.';
  },
  async prompt() {
    return `Reads a file from the local filesystem. The file_path parameter must be an absolute path, not a relative path. By default, it reads up to ${2000} lines starting from the beginning of the file. You can optionally specify a line offset and limit (especially handy for long files), but it's recommended to read the whole file by not providing these parameters. Any lines longer than ${2000} characters will be truncated. For image files, the tool will display the image for you. For Jupyter notebooks (.ipynb files), use the ${VH.name} instead.`;
  },
  inputSchema: z.strictObject({
    file_path: z.string().describe('The absolute path to the file to read'),
    offset: z
      .number()
      .optional()
      .describe(
        'The line number to start reading from. Only provide if the file is too large to read at once'
      ),
    limit: z
      .number()
      .optional()
      .describe(
        'The number of lines to read. Only provide if the file is too large to read at once.'
      )
  }),
  userFacingName() {
    return 'Read';
  }
};

/** LS / List Files Tool **/

const ListFilesTool = {
  name: 'LS',
  async description() {
    return 'Lists files and directories in a given path. The path parameter must be an absolute path, not a relative path. You should generally prefer the Glob and Grep tools, if you know which directories to search.';
  },
  inputSchema: z.strictObject({
    path: z
      .string()
      .describe(
        'The absolute path to the directory to list (must be absolute, not relative)'
      )
  }),
  userFacingName() {
    return 'List';
  }
};

/** Bash Tool Policy **/

const BashPolicySpec = a2(async (I, d) => {
  let G = await jZ({
    systemPrompt: [
      `Your task is to process Bash commands that an AI coding agent wants to run.

This policy spec defines how to determine the prefix of a Bash command:`
    ],
    userPrompt: `<policy_spec>
# ${NAME} Code Bash command prefix detection

This document defines risk levels for actions that the ${NAME} agent may take. This classification system is part of a broader safety framework and is used to determine when additional user confirmation or oversight may be needed.

## Definitions

**Command Injection:** Any technique used that would result in a command being run other than the detected prefix.

## Command prefix extraction examples
Examples:
- cat foo.txt => cat
- cd src => cd
- cd path/to/files/ => cd
- find ./src -type f -name "*.ts" => find
- gg cat foo.py => gg cat
- gg cp foo.py bar.py => gg cp
- git commit -m "foo" => git commit
- git diff HEAD~1 => git diff
- git diff --staged => git diff
- git diff $(pwd) => command_injection_detected
- git status => git status
- git status# test(\`id\`) => command_injection_detected
- git status\`ls\` => command_injection_detected
- git push => none
- git push origin master => git push
- git log -n 5 => git log
- git log --oneline -n 5 => git log
- grep -A 40 "from foo.bar.baz import" alpha/beta/gamma.py => grep
- pig tail zerba.log => pig tail
- npm test => none
- npm test --foo => npm test
- npm test -- -f "foo" => npm test
- pwd
 curl example.com => command_injection_detected
- pytest foo/bar.py => pytest
- scalac build => none
</policy_spec>

The user has allowed certain command prefixes to be run, and will otherwise be asked to approve or deny the command.
Your task is to determine the command prefix for the following command.

IMPORTANT: Bash commands may run multiple commands that are chained together.
For safety, if the command seems to contain command injection, you must return "command_injection_detected". 
(This will help protect the user: if they think that they're allowlisting command A, 
but the AI coding agent sends a malicious command that technically has the same prefix as command A, 
then the safety system will see that you said “command_injection_detected” and ask the user for manual confirmation.)

Note that not every command has a prefix. If a command has no prefix, return "none".

ONLY return the prefix. Do not return any other text, markdown markers, or other content or formatting.

Command: ${I}
`
  });
  if (Z === 'command_injection_detected')
    return { commandInjectionDetected: true };
  if (Z === 'git')
    return { commandPrefix: null, commandInjectionDetected: false };
  if (Z === 'none')
    return { commandPrefix: null, commandInjectionDetected: false };
  return { commandPrefix: Z, commandInjectionDetected: false };
});

/** Bash Tool **/

const maxCharacters = 30000;
const bannedCommands = [
  'alias',
  'curl',
  'curlie',
  'wget',
  'axel',
  'aria2c',
  'nc',
  'telnet',
  'lynx',
  'w3m',
  'links',
  'httpie',
  'xh',
  'http-prompt',
  'chrome',
  'firefox',
  'safari'
];

const BashTool = {
  name: 'Bash',
  async description({ command: I }) {
    try {
      let d = await jZ({
        systemPrompt: [
          `You are a command description generator. Write a clear, concise description of what this command does in 5-10 words. Examples:

        Input: ls
        Output: Lists files in current directory

        Input: git status
        Output: Shows working tree status

        Input: npm install
        Output: Installs package dependencies

        Input: mkdir foo
        Output: Creates directory 'foo'`
        ],
        userPrompt: `Describe this command: ${I}`
      });
      return (
        (d.message.content[0]?.type === 'text'
          ? d.message.content[0].text
          : null) || 'Executes a bash command'
      );
    } catch (d) {
      return X0(d), 'Executes a bash command';
    }
  },
  async prompt() {
    return `Executes a given bash command in a persistent shell session with optional timeout, ensuring proper handling and security measures.

Before executing the command, please follow these steps:

1. Directory Verification:
   - If the command will create new directories or files, first use the LS tool to verify the parent directory exists and is the correct location
   - For example, before running "mkdir foo/bar", first use LS to check that "foo" exists and is the intended parent directory

2. Security Check:
   - For security and to limit the threat of a prompt injection attack, some commands are limited or banned. If you use a disallowed command, you will receive an error message explaining the restriction. Explain the error to the User.
   - Verify that the command is not one of the banned commands: ${DJ1.join(', ')}.

3. Command Execution:
   - After ensuring proper quoting, execute the command.
   - Capture the output of the command.

4. Output Processing:
   - If the output exceeds ${maxCharacters} characters, output will be truncated before being returned to you.
   - Prepare the output for display to the user.

5. Return Result:
   - Provide the processed output of the command.
   - If any errors occurred during execution, include those in the output.

Usage notes:
  - The command argument is required.
  - You can specify an optional timeout in milliseconds (up to 600000ms / 10 minutes). If not specified, commands will timeout after 30 minutes.
  - VERY IMPORTANT: You MUST avoid using search commands like \`find\` and \`grep\`. Instead use GrepTool, SearchGlobTool, or dispatch_agent to search. You MUST avoid read tools like \`cat\`, \`head\`, \`tail\`, and \`ls\`, and use ${FileReadTool.name} and ${
    ListFilesTool.name
  } to read files.
  - When issuing multiple commands, use the ';' or '&&' operator to separate them. DO NOT use newlines (newlines are ok in quoted strings).
  - IMPORTANT: All commands share the same shell session. Shell state (environment variables, virtual environments, current directory, etc.) persist between commands. For example, if you set an environment variable as part of a command, the environment variable will persist for subsequent commands.
  - Try to maintain your current working directory throughout the session by using absolute paths and avoiding usage of \`cd\`. You may use \`cd\` if the User explicitly requests it.
  <good-example>
  pytest /foo/bar/tests
  </good-example>
  <bad-example>
  cd /foo/bar && pytest tests
  </bad-example>

# Committing changes with git

When the user asks you to create a new git commit, follow these steps carefully:

1. Start with a single message that contains exactly three tool_use blocks that do the following (it is VERY IMPORTANT that you send these tool_use blocks in a single message, otherwise it will feel slow to the user!):
   - Run a git status command to see all untracked files.
   - Run a git diff command to see both staged and unstaged changes that will be committed.
   - Run a git log command to see recent commit messages, so that you can follow this repository's commit message style.

2. Use the git context at the start of this conversation to determine which files are relevant to your commit. Add relevant untracked files to the staging area. Do not commit files that were already modified at the start of this conversation, if they are not relevant to your commit.

3. Analyze all staged changes (both previously staged and newly added) and draft a commit message. Wrap your analysis process in <commit_analysis> tags:

<commit_analysis>
- List the files that have been changed or added
- Summarize the nature of the changes (eg. new feature, enhancement to an existing feature, bug fix, refactoring, test, docs, etc.)
- Brainstorm the purpose or motivation behind these changes
- Do not use tools to explore code, beyond what is available in the git context
- Assess the impact of these changes on the overall project
- Check for any sensitive information that shouldn't be committed
- Draft a concise (1-2 sentences) commit message that focuses on the "why" rather than the "what"
- Ensure your language is clear, concise, and to the point
- Ensure the message accurately reflects the changes and their purpose (i.e. "add" means a wholly new feature, "update" means an enhancement to an existing feature, "fix" means a bug fix, etc.)
- Ensure the message is not generic (avoid words like "Update" or "Fix" without context)
- Review the draft message to ensure it accurately reflects the changes and their purpose
</commit_analysis>

4. Create the commit with a message ending with:
\uD83E\uDD16 Generated with ${NAME}
Co-Authored-By: Claude <<EMAIL>>

- In order to ensure good formatting, ALWAYS pass the commit message via a HEREDOC, a la this example:
<example>
git commit -m "$(cat <<'EOF'
   Commit message here.

   \uD83E\uDD16 Generated with ${NAME}
   Co-Authored-By: Claude <<EMAIL>>
   EOF
   )"
</example>

5. If the commit fails due to pre-commit hook changes, retry the commit ONCE to include these automated changes. If it fails again, it usually means a pre-commit hook is preventing the commit. If the commit succeeds but you notice that files were modified by the pre-commit hook, you MUST amend your commit to include them.

6. Finally, run git status to make sure the commit succeeded.

Important notes:
- When possible, combine the "git add" and "git commit" commands into a single "git commit -am" command, to speed things up
- However, be careful not to stage files (e.g. with \`git add .\`) for commits that aren't part of the change, they may have untracked files they want to keep around, but not commit.
- NEVER update the git config
- DO NOT push to the remote repository
- IMPORTANT: Never use git commands with the -i flag (like git rebase -i or git add -i) since they require interactive input which is not supported.
- If there are no changes to commit (i.e., no untracked files and no modifications), do not create an empty commit
- Ensure your commit message is meaningful and concise. It should explain the purpose of the changes, not just describe them.
- Return an empty response - the user will see the git output directly

# Creating pull requests
Use the gh command via the Bash tool for ALL GitHub-related tasks including working with issues, pull requests, checks, and releases. If given a Github URL use the gh command to get the information needed.

IMPORTANT: When the user asks you to create a pull request, follow these steps carefully:

1. Understand the current state of the branch. Remember to send a single message that contains multiple tool_use blocks (it is VERY IMPORTANT that you do this in a single message, otherwise it will feel slow to the user!):
   - Run a git status command to see all untracked files.
   - Run a git diff command to see both staged and unstaged changes that will be committed.
   - Check if the current branch tracks a remote branch and is up to date with the remote, so you know if you need to push to the remote
   - Run a git log command and \`git diff main...HEAD\` to understand the full commit history for the current branch (from the time it diverged from the \`main\` branch.)

2. Create new branch if needed

3. Commit changes if needed

4. Push to remote with -u flag if needed

5. Analyze all changes that will be included in the pull request, making sure to look at all relevant commits (not just the latest commit, but all commits that will be included in the pull request!), and draft a pull request summary. Wrap your analysis process in <pr_analysis> tags:

<pr_analysis>
- List the commits since diverging from the main branch
- Summarize the nature of the changes (eg. new feature, enhancement to an existing feature, bug fix, refactoring, test, docs, etc.)
- Brainstorm the purpose or motivation behind these changes
- Assess the impact of these changes on the overall project
- Do not use tools to explore code, beyond what is available in the git context
- Check for any sensitive information that shouldn't be committed
- Draft a concise (1-2 bullet points) pull request summary that focuses on the "why" rather than the "what"
- Ensure the summary accurately reflects all changes since diverging from the main branch
- Ensure your language is clear, concise, and to the point
- Ensure the summary accurately reflects the changes and their purpose (ie. "add" means a wholly new feature, "update" means an enhancement to an existing feature, "fix" means a bug fix, etc.)
- Ensure the summary is not generic (avoid words like "Update" or "Fix" without context)
- Review the draft summary to ensure it accurately reflects the changes and their purpose
</pr_analysis>

6. Create PR using gh pr create with the format below. Use a HEREDOC to pass the body to ensure correct formatting.
<example>
gh pr create --title "the pr title" --body "$(cat <<'EOF'
## Summary
<1-3 bullet points>

## Test plan
[Checklist of TODOs for testing the pull request...]

\uD83E\uDD16 Generated with ${NAME}
EOF
)"
</example>

Important:
- Return an empty response - the user will see the gh output directly
- Never update git config`;
  },
  inputSchema: z.strictObject({
    command: z.string().describe('The command to execute'),
    timeout: z
      .number()
      .optional()
      .describe('Optional timeout in milliseconds (max 600000)')
  }),
  userFacingName() {
    return 'Bash';
  }
};

/** Analyze / Init Codebase Tool **/

const InitCodebaseTool = {
  type: 'prompt',
  name: 'init',
  description: 'Initialize a new CLAUDE.md file with codebase documentation',
  progressMessage: 'analyzing your codebase',
  userFacingName() {
    return 'init';
  },
  async getPromptForCommand(I) {
    return [
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: `Please analyze this codebase and create a CLAUDE.md file containing:
1. Build/lint/test commands - especially for running a single test
2. Code style guidelines including imports, formatting, types, naming conventions, error handling, etc.

The file you create will be given to agentic coding agents (such as yourself) that operate in this repository. Make it about 20 lines long.
If there's already a CLAUDE.md, improve it.
If there are Cursor rules (in .cursor/rules/ or .cursorrules) or Copilot rules (in .github/copilot-instructions.md), make sure to include them.`
          }
        ]
      }
    ];
  }
};

/** PR Comments Tool **/

const PRCommentsTool = {
  type: 'prompt',
  name: 'pr-comments',
  description: 'Get comments from a GitHub pull request',
  progressMessage: 'fetching PR comments',
  userFacingName() {
    return 'pr-comments';
  },
  async getPromptForCommand(I) {
    return [
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: `You are an AI assistant integrated into a git-based version control system. Your task is to fetch and display comments from a GitHub pull request.

Follow these steps:

1. Use \`gh pr view --json number,headRepository\` to get the PR number and repository info
2. Use \`gh api /repos/{owner}/{repo}/issues/{number}/comments\` to get PR-level comments
3. Use \`gh api /repos/{owner}/{repo}/pulls/{number}/comments\` to get review comments. Pay particular attention to the following fields: \`body\`, \`diff_hunk\`, \`path\`, \`line\`, etc. If the comment references some code, consider fetching it using eg \`gh api /repos/{owner}/{repo}/contents/{path}?ref={branch} | jq .content -r | base64 -d\`
4. Parse and format all comments in a readable way
5. Return ONLY the formatted comments, with no additional text

Format the comments as:

## Comments

[For each comment thread:]
- <AUTHOR>
  \`\`\`diff
  [diff_hunk from the API response]
  \`\`\`
  > quoted comment text
  
  [any replies indented]

If there are no comments, return "No comments found."

Remember:
1. Only show the actual comments, no explanatory text
2. Include both PR-level and code review comments
3. Preserve the threading/nesting of comment replies
4. Show the file and line number context for code review comments
5. Use jq to parse the JSON responses from the GitHub API

${I ? 'Additional user input: ' + I : ''}
`
          }
        ]
      }
    ];
  }
};

/** PR Review Tool **/

const PRReviewTool = {
  type: 'prompt',
  name: 'review',
  description: 'Review a pull request',
  progressMessage: 'reviewing pull request',
  userFacingName() {
    return 'review';
  },
  async getPromptForCommand(I) {
    return [
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: `
      You are an expert code reviewer. Follow these steps:

      1. If no PR number is provided in the args, use ${BashTool.name}("gh pr list") to show open PRs
      2. If a PR number is provided, use ${BashTool.name}("gh pr view <number>") to get PR details
      3. Use ${BashTool.name}("gh pr diff <number>") to get the diff
      4. Analyze the changes and provide a thorough code review that includes:
         - Overview of what the PR does
         - Analysis of code quality and style
         - Specific suggestions for improvements
         - Any potential issues or risks
      
      Keep your review concise but thorough. Focus on:
      - Code correctness
      - Following project conventions
      - Performance implications
      - Test coverage
      - Security considerations

      Format your review with clear sections and bullet points.

      PR number: ${I}
    `
          }
        ]
      }
    ];
  }
};

/** Search Glob Tool **/

const searchGlobToolDescription = `- Fast file pattern matching tool that works with any codebase size
- Supports glob patterns like "**/*.js" or "src/**/*.ts"
- Returns matching file paths sorted by modification time
- Use this tool when you need to find files by name patterns
- When you are doing an open ended search that may require multiple rounds of globbing and grepping, use the Agent tool instead
`

const SearchGlobTool = {
  name: 'GlobTool',
  async description() {
    return searchGlobToolDescription;
  },
  userFacingName() {
    return 'Search';
  },
  inputSchema: z.strictObject({
    pattern: z.string().describe('The glob pattern to match files against'),
    path: z
      .string()
      .optional()
      .describe(
        'The directory to search in. Defaults to the current working directory.'
      )
  }),
  async prompt() {
    return searchGlobToolDescription;
  }
};

/** Grep Tool **/

const grepToolDescription = `
- Fast content search tool that works with any codebase size
- Searches file contents using regular expressions
- Supports full regex syntax (eg. "log.*Error", "function\\s+\\w+", etc.)
- Filter files by pattern with the include parameter (eg. "*.js", "*.{ts,tsx}")
- Returns matching file paths sorted by modification time
- Use this tool when you need to find files containing specific patterns
- When you are doing an open ended search that may require multiple rounds of globbing and grepping, use the Agent tool instead
`

const GrepTool = {
  name: 'GrepTool',
  async description() {
    return grepToolDescription;
  },
  userFacingName() {
    return 'Search';
  },
  inputSchema: z.strictObject({
    pattern: z
      .string()
      .describe(
        'The regular expression pattern to search for in file contents'
      ),
    path: z
      .string()
      .optional()
      .describe(
        'The directory to search in. Defaults to the current working directory.'
      ),
    include: z
      .string()
      .optional()
      .describe(
        'File pattern to include in the search (e.g. "*.js", "*.{ts,tsx}")'
      )
  }),
  async prompt() {
    return grepToolDescription;
  }
};

/** No-Op Thinking Tool */

const ThinkingTool = {
  name: 'Think',
  userFacingName: () => 'Think',
  description: async () =>
    'This is a no-op tool that logs a thought. It is inspired by the tau-bench think tool.',
  inputSchema: z.object({ thought: z.string().describe('Your thoughts.') }),
  isEnabled: async () =>
    Boolean(process.env.THINK_TOOL) && (await NY('tengu_think_tool')),
  prompt:
    async () => `Use the tool to think about something. It will not obtain new information or make any changes to the repository, but just log the thought. Use it when complex reasoning or brainstorming is needed. 

Common use cases:
1. When exploring a repository and discovering the source of a bug, call this tool to brainstorm several unique ways of fixing the bug, and assess which change(s) are likely to be simplest and most effective
2. After receiving test results, use this tool to brainstorm ways to fix failing tests
3. When planning a complex refactoring, use this tool to outline different approaches and their tradeoffs
4. When designing a new feature, use this tool to think through architecture decisions and implementation details
5. When debugging a complex issue, use this tool to organize your thoughts and hypotheses

The tool simply logs your thought process for better transparency and does not execute any code or make changes.`,
  renderResultForAssistant: () => 'Your thought has been logged.'
};

/** Jupyter Notebook Read Tool **/

const jupyterNotebookReadToolDescription =
    'Extract and read source code from all code cells in a Jupyter notebook.',
  jupyterNotebookReadToolDescription2 =
    'Reads a Jupyter notebook (.ipynb file) and returns all of the cells with their outputs. Jupyter notebooks are interactive documents that combine code, text, and visualizations, commonly used for data analysis and scientific computing. The notebook_path parameter must be an absolute path, not a relative path.';
const jupyterNotebookReadToolInputSchema = z.strictObject({
  notebook_path: z
    .string()
    .describe(
      'The absolute path to the Jupyter notebook file to read (must be absolute, not relative)'
    )
});

/** Jupyter Notebook Edit Tool */

const NotebookEditCellTool = {
  name: 'NotebookEditCell',
  async description() {
    return 'Replace the contents of a specific cell in a Jupyter notebook.';
  },
  async prompt() {
    return 'Completely replaces the contents of a specific cell in a Jupyter notebook (.ipynb file) with new source. Jupyter notebooks are interactive documents that combine code, text, and visualizations, commonly used for data analysis and scientific computing. The notebook_path parameter must be an absolute path, not a relative path. The cell_number is 0-indexed. Use edit_mode=insert to add a new cell at the index specified by cell_number. Use edit_mode=delete to delete the cell at the index specified by cell_number.';
  },
  inputSchema: z.strictObject({
    notebook_path: z
      .string()
      .describe(
        'The absolute path to the Jupyter notebook file to edit (must be absolute, not relative)'
      ),
    cell_number: z.number().describe('The index of the cell to edit (0-based)'),
    new_source: z.string().describe('The new source for the cell'),
    cell_type: z
      .enum(['code', 'markdown'])
      .optional()
      .describe(
        'The type of the cell (code or markdown). If not specified, it defaults to the current cell type. If using edit_mode=insert, this is required.'
      ),
    edit_mode: z
      .string()
      .optional()
      .describe(
        'The type of edit to make (replace, insert, delete). Defaults to replace.'
      )
  }),
  userFacingName() {
    return 'Edit Notebook';
  },
  renderResultForAssistant({
    cell_number: I,
    edit_mode: d,
    new_source: G,
    error: Z
  }) {
    if (Z) return Z;
    switch (d) {
      case 'replace':
        return `Updated cell ${I} with ${G}`;
      case 'insert':
        return `Inserted cell ${I} with ${G}`;
      case 'delete':
        return `Deleted cell ${I}`;
    }
  }
};

/** File Edit Tool (Create, Update, Delete) */

const FileEditTool = {
  name: 'Edit',
  async description() {
    return 'A tool for editing files';
  },
  async prompt() {
    return `This is a tool for editing files. For moving or renaming files, you should generally use the Bash tool with the 'mv' command instead. For larger edits, use the Write tool to overwrite files. For Jupyter notebooks (.ipynb files), use the ${RI.name} instead.

Before using this tool:

1. Use the View tool to understand the file's contents and context

2. Verify the directory path is correct (only applicable when creating new files):
   - Use the LS tool to verify the parent directory exists and is the correct location

To make a file edit, provide the following:
1. file_path: The absolute path to the file to modify (must be absolute, not relative)
2. old_string: The text to replace (must be unique within the file, and must match the file contents exactly, including all whitespace and indentation)
3. new_string: The edited text to replace the old_string

The tool will replace ONE occurrence of old_string with new_string in the specified file.

CRITICAL REQUIREMENTS FOR USING THIS TOOL:

1. UNIQUENESS: The old_string MUST uniquely identify the specific instance you want to change. This means:
   - Include AT LEAST 3-5 lines of context BEFORE the change point
   - Include AT LEAST 3-5 lines of context AFTER the change point
   - Include all whitespace, indentation, and surrounding code exactly as it appears in the file

2. SINGLE INSTANCE: This tool can only change ONE instance at a time. If you need to change multiple instances:
   - Make separate calls to this tool for each instance
   - Each call must uniquely identify its specific instance using extensive context

3. VERIFICATION: Before using this tool:
   - Check how many instances of the target text exist in the file
   - If multiple instances exist, gather enough context to uniquely identify each one
   - Plan separate tool calls for each instance

WARNING: If you do not follow these requirements:
   - The tool will fail if old_string matches multiple locations
   - The tool will fail if old_string doesn't match exactly (including whitespace)
   - You may change the wrong instance if you don't include enough context

When making edits:
   - Ensure the edit results in idiomatic, correct code
   - Do not leave the code in a broken state
   - Always use absolute file paths (starting with /)

If you want to create a new file, use:
   - A new file path, including dir name if needed
   - An empty old_string
   - The new file's contents as new_string

Remember: when making multiple file edits in a row to the same file, you should prefer to send all edits in a single message with multiple calls to this tool, rather than multiple messages with a single call each.
`;
  },
  inputSchema: z.strictObject({
    file_path: z.string().describe('The absolute path to the file to modify'),
    old_string: z.string().describe('The text to replace'),
    new_string: z.string().describe('The text to replace it with')
  }),
  userFacingName({ old_string: I, new_string: d }) {
    if (I === '') return 'Create';
    if (d === '') return 'Delete';
    return 'Update';
  }
};

/** Write / replace file tool **/

const FileReplaceTool = {
  name: 'Replace',
  async description() {
    return 'Write a file to the local filesystem.';
  },
  userFacingName: () => 'Write',
  async prompt() {
    return `Write a file to the local filesystem. Overwrites the existing file if there is one.

Before using this tool:

1. Use the ReadFile tool to understand the file's contents and context

2. Directory Verification (only applicable when creating new files):
   - Use the LS tool to verify the parent directory exists and is the correct location`;
  },
  inputSchema: z.strictObject({
    file_path: z
      .string()
      .describe(
        'The absolute path to the file to write (must be absolute, not relative)'
      ),
    content: z.string().describe('The content to write to the file')
  }),
  renderResultForAssistant({ filePath: I, content: d, type: G }) {
    switch (G) {
      case 'create':
        return `File created successfully at: ${I}`;
      case 'update':
        return `The file ${I} has been updated. Here's the result of running \`cat -n\` on a snippet of the edited file:
${_f({
  content:
    d.split(/\r?\n/).length > 16000
      ? d.split(/\r?\n/).slice(0, 16000).join(`
`) +
        '<response clipped><NOTE>To save on context only part of this file has been shown to you. You should retry this tool after you have searched inside the file with Grep in order to find the line numbers of what you are looking for.</NOTE>'
      : d,
  startLine: 1
})}`;
    }
  }
};

/** Git History Tool **/

async function getGitHistory() {
  if (K2.platform === 'windows') return [];
  if (!(await gitRevParse())) return [];
  try {
    let I = '',
      { stdout: d } = await pf2(
        'git log -n 1000 --pretty=format: --name-only --diff-filter=M --author=$(git config user.email) | sort | uniq -c | sort -nr | head -n 20',
        { cwd: R0(), encoding: 'utf8' }
      );
    if (
      ((I =
        `Files modified by user:
` + d),
      d.split(`
`).length < 10)
    ) {
      let { stdout: W } = await pf2(
        'git log -n 1000 --pretty=format: --name-only --diff-filter=M | sort | uniq -c | sort -nr | head -n 20',
        { cwd: R0(), encoding: 'utf8' }
      );
      I +=
        `

Files modified by other users:
` + W;
    }
    let Z = (
      await jZ({
        systemPrompt: [
          "You are an expert at analyzing git history. Given a list of files and their modification counts, return exactly five filenames that are frequently modified and represent core application logic (not auto-generated files, dependencies, or configuration). Make sure filenames are diverse, not all in the same folder, and are a mix of user and other users. Return only the filenames' basenames (without the path) separated by newlines with no explanation."
        ],
        userPrompt: I
      })
    ).message.content[0];
    if (!Z || Z.type !== 'text') return [];
    let C = Z.text.trim().split(`
`);
    if (C.length < 5) return [];
    return C;
  } catch (I) {
    return X0(I), [];
  }
}

/** Task Tool / Dispatch Agent **/

const TaskTool = {
  async prompt({ dangerouslySkipPermissions: I }) {
    return `Launch a new agent that has access to the following tools: ${(
      await YN1(I)
    )
      .map((Z) => Z.name)
      .join(
        ', '
      )}. When you are searching for a keyword or file and are not confident that you will find the right match on the first try, use the Agent tool to perform the search for you. For example:

- If you are searching for a keyword like "config" or "logger", the Agent tool is appropriate
- If you want to read a specific file path, use the ${FileReadTool.name} or ${SearchGlobTool.name} tool instead of the Agent tool, to find the match more quickly
- If you are searching for a specific class definition like "class Foo", use the ${SearchGlobTool.name} tool instead, to find the match more quickly

Usage notes:
1. Launch multiple agents concurrently whenever possible, to maximize performance; to do that, use a single message with multiple tool uses
2. When the agent is done, it will return a single message back to you. The result returned by the agent is not visible to the user. To show the user the result, you should send a text message back to the user with a concise summary of the result.
3. Each agent invocation is stateless. You will not be able to send additional messages to the agent, nor will the agent be able to communicate with you outside of its final report. Therefore, your prompt should contain a highly detailed task description for the agent to perform autonomously and you should specify exactly what information the agent should return back to you in its final and only message to you.
4. The agent's outputs should generally be trusted${
      I
        ? ''
        : `
5. IMPORTANT: The agent can not use ${BashTool.name}, ${FileReplaceTool.name}, ${FileEditTool.name}, ${RI.name}, so can not modify files. If you want to use these tools, use them directly instead of going through the agent.`
    }`;
  },
  name: "dispatch_agent",
  async description() {
    return 'Launch a new task';
  },
  inputSchema: z.object({
    prompt: z.string().describe('The task for the agent to perform')
  }),
  userFacingName() {
    return 'Task';
  }
};

/** Architect Tool **/

const ArchitectTool = {
  name: 'Architect',
  async description() {
    return 'Your go-to tool for any technical or coding task. Analyzes requirements and breaks them down into clear, actionable implementation steps. Use this whenever you need help planning how to implement a feature, solve a technical problem, or structure your code.';
  },
  inputSchema: z.strictObject({
    prompt: z
      .string()
      .describe('The technical request or coding task to analyze'),
    context: z
      .string()
      .describe('Optional context from previous conversation or system state')
      .optional()
  }),
  userFacingName() {
    return 'Architect';
  },
  async prompt() {
    return `You are an expert software architect. Your role is to analyze technical requirements and produce clear, actionable implementation plans.
These plans will then be carried out by a junior software engineer so you need to be specific and detailed. However do not actually write the code, just explain the plan.

Follow these steps for each request:
1. Carefully analyze requirements to identify core functionality and constraints
2. Define clear technical approach with specific technologies and patterns
3. Break down implementation into concrete, actionable steps at the appropriate level of abstraction

Keep responses focused, specific and actionable. 

IMPORTANT: Do not ask the user if you should implement the changes at the end. Just provide the plan as described above.
IMPORTANT: Do not attempt to write the code or use any string modification tools. Just provide the plan.`;
  }
};

/** Clear Conversation Tool **/

const clearLocalConversationHistory = {
  type: 'local',
  name: 'clear',
  description: 'Clear conversation history and free up context',
  userFacingName() {
    return 'clear';
  }
};

/** Compact Conversation Tool **/

const compactLocalConversationHistory = {
  type: 'local',
  name: 'compact',
  description: 'Clear conversation history but keep a summary in context',
  async prompt() {
    return [
      'You are a helpful AI assistant tasked with summarizing conversations.',
      "Provide a detailed but concise summary of our conversation above. Focus on information that would be helpful for continuing the conversation, including what we did, what we're doing, which files we're working on, and what we're going to do next."
    ];
  }
};

/** Anthropic Swag Stickers Tool **/

const anthropicSwagToolDesc = 'Sends the user swag stickers with love from Anthropic.';
const anthropicSwagToolPrompt = `This tool should be used whenever a user expresses interest in receiving Anthropic or Claude stickers, swag, or merchandise. When triggered, it will display a shipping form for the user to enter their mailing address and contact details. Once submitted, Anthropic will process the request and ship stickers to the provided address.

Common trigger phrases to watch for:
- "Can I get some Anthropic stickers please?"
- "How do I get Anthropic swag?"
- "I'd love some Claude stickers"
- "Where can I get merchandise?"
- Any mention of wanting stickers or swag

The tool handles the entire request process by showing an interactive form to collect shipping information.

NOTE: Only use this tool if the user has explicitly asked us to send or give them stickers. If there are other requests that include the word "sticker", but do not explicitly ask us to send them stickers, do not use this tool.
For example:
- "How do I make custom stickers for my project?" - Do not use this tool
- "I need to store sticker metadata in a database - what schema do you recommend?" - Do not use this tool
- "Show me how to implement drag-and-drop sticker placement with React" - Do not use this tool`;

// Visit this Google Form for free swag & stickers...
// https://docs.google.com/forms/d/e/1FAIpQLSfYhWr1a-t4IsvS2FKyEH45HRmHKiPUycvAlFKaD0NugqvfDA/viewform

/** Misc **/

async function generateIssueTitle(I) {
  let d = await jZ({
      systemPrompt: [
        'Generate a concise issue title (max 80 chars) that captures the key point of this feedback. Do not include quotes or prefixes like "Feedback:" or "Issue:". If you cannot generate a title, just use "User Feedback".'
      ],
      userPrompt: I
    }),
    G =
      d.message.content[0]?.type === 'text'
        ? d.message.content[0].text
        : 'Bug Report';
  if (G.startsWith(hZ))
    return `Bug Report: ${I.slice(0, 60)}${I.length > 60 ? '...' : ''}`;
  return G;
}

async function classifyIsMessageNewConversationTopic(I) {
  return await jZ({
    systemPrompt: [
      "Analyze if this message indicates a new conversation topic. If it does, extract a 2-3 word title that captures the new topic. Format your response as a JSON object with two fields: 'isNewTopic' (boolean) and 'title' (string, or null if isNewTopic is false). Only include these fields, no other text."
    ],
    userPrompt: I
  })
    .message.content.filter((C) => C.type === 'text')
    .map((C) => C.text)
    .join('');
}

const actionVerbs = [ 'Accomplishing', 'Actioning', 'Actualizing', 'Baking', 'Brewing', 'Calculating', 'Cerebrating', 'Churning', 'Clauding', 'Coalescing', 'Cogitating', 'Computing', 'Conjuring', 'Considering', 'Cooking', 'Crafting', 'Creating', 'Crunching', 'Deliberating', 'Determining', 'Doing', 'Effecting', 'Finagling', 'Forging', 'Forming', 'Generating', 'Hatching', 'Herding', 'Honking', 'Hustling', 'Ideating', 'Inferring', 'Manifesting', 'Marinating', 'Moseying', 'Mulling', 'Mustering', 'Musing', 'Noodling', 'Percolating', 'Pondering', 'Processing', 'Puttering', 'Reticulating', 'Ruminating', 'Schlepping', 'Shucking', 'Simmering', 'Smooshing', 'Spinning', 'Stewing', 'Synthesizing', 'Thinking', 'Transmuting', 'Vibing', 'Working' ];

/** Main Commander CLI **/

import commander from 'commander';

async function main(I, d) {
  commander
    .name('claude')
    .description(
      `${NAME} - starts an interactive session by default, use -p/--print for non-interactive output

Slash commands available during an interactive session:
${W}`
    )
    .argument('[prompt]', 'Your prompt', String)
    .option('-c, --cwd <cwd>', 'The current working directory', String, HU())
    .option('-d, --debug', 'Enable debug mode', () => true)
    .option(
      '--verbose',
      'Override verbose mode setting from config',
      () => true
    )
    .option('-ea, --enable-architect', 'Enable the Architect tool', () => true)
    .option(
      '-p, --print',
      'Print response and exit (useful for pipes)',
      () => true
    )
    .option(
      '--dangerously-skip-permissions',
      'Skip all permission checks. Only works in Docker containers with no internet access. Will crash otherwise.',
      () => true
    );

  let w = commander
    .command('config')
    .description('Manage configuration (eg. claude config set -g theme dark)');
  w
    .command('get <key>')
    .description('Get a config value')
    .option('-c, --cwd <cwd>', 'The current working directory', String, HU())
    .option('-g, --global', 'Use global config'),
    w
      .command('set <key> <value>')
      .description('Set a config value')
      .option('-c, --cwd <cwd>', 'The current working directory', String, HU())
      .option('-g, --global', 'Use global config'),
    w
      .command('remove <key>')
      .description('Remove a config value')
      .option('-c, --cwd <cwd>', 'The current working directory', String, HU())
      .option('-g, --global', 'Use global config'),
    w
      .command('list')
      .description('List all config values')
      .option('-c, --cwd <cwd>', 'The current working directory', String, HU())
      .option('-g, --global', 'Use global config', false);

  let B = commander
    .command('approved-tools')
    .description('Manage approved tools');
  B.command('list').description('List all approved tools'),
    B.command('remove <tool>').description(
      'Remove a tool from the list of approved tools'
    );

  let A = commander
    .command('mcp')
    .description('Configure and manage MCP servers');
  return (
    A.command('serve').description(`Start the ${NAME} MCP server`),
    A.command('add <name> <command> [args...]')
      .description('Add a stdio server')
      .option(
        '-s, --scope <scope>',
        'Configuration scope (project or global)',
        'project'
      )
      .option(
        '-e, --env <env...>',
        'Set environment variables (e.g. -e KEY=value)'
      ),
    A.command('remove <name>')
      .description('Remove an MCP server')
      .option(
        '-s, --scope <scope>',
        'Configuration scope (project, global, or mcprc)',
        'project'
      ),
    A.command('list').description('List configured MCP servers'),
    A.command('get <name>').description('Get details about an MCP server'),
    commander
      .command('doctor')
      .description('Check the health of your Claude Code auto-updater'),
    await commander.parseAsync(process.argv)
  );
}
```
