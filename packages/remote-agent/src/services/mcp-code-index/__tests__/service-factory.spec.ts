import { MCPCodeIndexServiceFactory } from '../service-factory';
import { MCPCodeIndexConfig } from '../config';
import { Logger } from '../../../shared/logger';

describe('MCPCodeIndexServiceFactory', () => {
  it('should create a MCPCodeIndexManager instance', () => {
    const config = new MCPCodeIndexConfig();
    const logger = new Logger();
    const manager = MCPCodeIndexServiceFactory.create(config, logger);
    expect(manager).toBeInstanceOf(MCPCodeIndexManager);
  });
});