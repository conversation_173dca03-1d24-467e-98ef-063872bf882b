import { MCPCodeIndexConfig } from '../config';
import { describe, it, expect } from 'vitest';

describe('MCPCodeIndexConfig', () => {
  it('should initialize with default values', () => {
    const config = new MCPCodeIndexConfig();
    expect(config.enabled).toBe(false);
    expect(config.embedderType).toBe('openai');
  });

  it('should override default values', () => {
    const config = new MCPCodeIndexConfig({ enabled: true, embedderType: 'ollama' });
    expect(config.enabled).toBe(true);
    expect(config.embedderType).toBe('ollama');
  });
});