import { MCPCodeIndexConfig } from './config';
import { Logger } from '../../shared/logger';

export class MCPCodeIndexManager {
  private config: MCPCodeIndexConfig;
  private logger: Logger;

  constructor() {
    this.logger = new Logger('MCPCodeIndexManager');
    this.config = { enabled: true, endpoint: 'http://localhost:8080' };
  }

  {{ ... existing code ... }}

  private handleError(error: Error): void {
    this.logger.error(`Error: ${error.message}`);
    throw error;
  }
}