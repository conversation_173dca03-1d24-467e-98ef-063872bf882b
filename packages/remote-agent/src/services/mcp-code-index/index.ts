import { MCPCodeIndexManager } from './manager';
import { MCPCodeIndexConfig } from './interfaces';

export class MCPCodeIndexService {
  private manager: MCPCodeIndexManager;

  constructor(config: MCPCodeIndexConfig) {
    this.manager = new MCPCodeIndexManager(config);
  }

  public async start(): Promise<void> {
    await this.manager.initialize();
  }

  public async stop(): Promise<void> {
    await this.manager.shutdown();
  }
}