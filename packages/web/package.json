{"name": "@autodev/web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma generate && next build", "build:ci": "cp env.example .env && prisma generate && next build", "start": "next start", "lint": "next lint", "prepublishOnly": "pnpm run build"}, "packageManager": "pnpm@10.10.0", "dependencies": {"@ai-sdk/openai": "^1.3.22", "@babel/standalone": "^7.27.2", "@codemirror/lang-markdown": "^6.3.2", "@hookform/resolvers": "^3.10.0", "@neondatabase/serverless": "^1.0.0", "@next-auth/prisma-adapter": "^1.0.7", "@radix-ui/react-accordion": "1.2.10", "@radix-ui/react-alert-dialog": "1.1.13", "@radix-ui/react-aspect-ratio": "1.1.6", "@radix-ui/react-avatar": "1.1.9", "@radix-ui/react-checkbox": "1.3.1", "@radix-ui/react-collapsible": "1.1.10", "@radix-ui/react-context-menu": "2.2.14", "@radix-ui/react-dialog": "1.1.13", "@radix-ui/react-dropdown-menu": "2.1.14", "@radix-ui/react-hover-card": "1.1.13", "@radix-ui/react-label": "2.1.6", "@radix-ui/react-menubar": "1.1.14", "@radix-ui/react-navigation-menu": "1.2.12", "@radix-ui/react-popover": "1.1.13", "@radix-ui/react-progress": "1.1.6", "@radix-ui/react-radio-group": "1.3.6", "@radix-ui/react-scroll-area": "1.2.8", "@radix-ui/react-select": "2.2.4", "@radix-ui/react-separator": "1.1.6", "@radix-ui/react-slider": "1.3.4", "@radix-ui/react-slot": "1.2.2", "@radix-ui/react-switch": "1.2.4", "@radix-ui/react-tabs": "1.1.11", "@radix-ui/react-toast": "1.2.13", "@radix-ui/react-toggle": "1.1.8", "@radix-ui/react-toggle-group": "1.1.9", "@radix-ui/react-tooltip": "1.2.6", "@types/plantuml-encoder": "^1.4.2", "@types/swagger-ui-react": "^5.18.0", "@types/xml2js": "^0.4.14", "@uiw/react-codemirror": "^4.23.12", "@vercel/postgres": "^0.10.0", "ai": "^4.3.15", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "eventsource-parser": "^3.0.1", "framer-motion": "^12.10.5", "husky": "^9.1.7", "input-otp": "^1.4.2", "keyword-extractor": "^0.0.28", "lru-cache": "^10.4.3", "lucide-react": "^0.509.0", "mermaid": "^11.6.0", "natural": "^8.0.1", "next": "15.3.1", "next-auth": "^4.24.5", "next-themes": "^0.4.6", "nodejieba": "3.4.3", "plantuml-encoder": "^1.4.0", "prismjs": "^1.30.0", "react-day-picker": "^8.10.1", "react-graph-vis": "^1.0.7", "react-hook-form": "^7.56.3", "react-live": "^4.1.8", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.1", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.3", "remark-gfm": "^4.0.1", "remark-parse": "^11.0.0", "rimraf": "^6.0.1", "sonner": "^2.0.3", "svg-pan-zoom": "^3.6.2", "swagger-ui-react": "^5.21.0", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.9", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "uuid": "^11.1.0", "vaul": "^1.1.2", "vis": "4.21.0-EOL", "vis-network": "^9.1.10", "xml2js": "^0.6.2", "xmlbuilder": "^15.1.1", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@prisma/client": "^6.7.0", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@types/chai": "^5.2.2", "@types/mocha": "^10.0.10", "@types/node": "^20", "@types/prismjs": "^1.26.5", "@types/react": "^19.1.3", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "@types/vis": "^4.21.27", "@vitejs/plugin-react": "^4.5.0", "chai": "^5.2.0", "eslint": "^9", "eslint-config-next": "15.3.1", "jsdom": "^24.1.3", "postcss": "^8", "prisma": "^6.7.0", "tailwindcss": "^3.4.17", "typescript": "^5", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.4"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}}