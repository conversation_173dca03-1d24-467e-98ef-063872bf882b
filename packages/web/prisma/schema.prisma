generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["postgresqlExtensions"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model CodeAnalysis {
  id          String   @id @default(cuid())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  path        String
  content     String
  description String?
  language    String?
  source      String?
  title       String?

  // Project relation
  projectId String?
  project   Project? @relation(fields: [projectId], references: [id])
}

model Conversations {
  id            String          @id
  title         String?
  createdAt     DateTime        @default(now()) @db.Timestamp(6)
  updatedAt     DateTime        @default(now()) @db.Timestamp(6)
  GeneratedCode GeneratedCode[]
  Messages      Messages[]
}

model GeneratedCode {
  id             String        @id
  conversationId String
  messageId      String
  code           String
  language       String
  title          String?
  description    String?
  createdAt      DateTime      @default(now()) @db.Timestamp(6)
  Conversations  Conversations @relation(fields: [conversationId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  Messages       Messages      @relation(fields: [messageId], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model Messages {
  id             String          @id
  conversationId String
  role           String
  content        String
  createdAt      DateTime        @default(now()) @db.Timestamp(6)
  GeneratedCode  GeneratedCode[]
  Conversations  Conversations   @relation(fields: [conversationId], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

enum Status {
  DRAFT
  PUBLISHED
  ARCHIVED
}

model Guideline {
  id          Int      @id @default(autoincrement())
  title       String
  description String?  @db.Text
  category    Json
  language    String   @default("general")
  content     String   @db.Text
  version     String?
  lastUpdated DateTime @default(now())
  popularity  Int      @default(0)
  status      Status   @default(DRAFT)
  createdBy   String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  // Project relation (optional to support global guidelines)
  projectId   String?
  project     Project? @relation(fields: [projectId], references: [id])
}

model Account {
  id                String  @id @default(cuid())
  userId            String  @map("user_id")
  type              String
  provider          String
  providerAccountId String  @map("provider_account_id")
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique @map("session_token")
  userId       String   @map("user_id")
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime? @map("email_verified")
  image         String?
  accounts      Account[]
  sessions      Session[]
  projects      Project[]
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  goldenPathConfigs GoldenPathConfig[]

  @@map("users")
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// 新增项目模型
model Project {
  id          String   @id @default(cuid())
  name        String
  description String?  @db.Text
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now())

  // 平台URL地址
  liveUrl    String? // 线上地址
  gitUrl     String // Git地址
  jiraUrl    String? // Jira地址
  jenkinsUrl String? // Jenkins地址

  // 可扩展的DevOps信息
  devOpsInfo Json?

  // 关联
  guidelines          Guideline[]
  codeAnalyses        CodeAnalysis[]
  conceptDictionaries ConceptDictionary[]
  apiResources        ApiResource[]
  symbolAnalyses      SymbolAnalysis[]
  goldenPathConfigs GoldenPathConfig[]

  isDefault Boolean @default(false)
  isPublic  Boolean @default(false)

  userId String?
  user   User?   @relation(fields: [userId], references: [id])
}

model ConceptDictionary {
  id           String   @id @default(cuid())
  termChinese  String // 中文词汇
  termEnglish  String // 英文词汇
  descChinese  String   @db.Text // 中文描述
  descEnglish  String   @db.Text // 英文描述
  relatedTerms String[] @default([]) // IDs of related concepts
  createdAt    DateTime @default(now())
  updatedAt    DateTime @default(now())

  projectId String?
  project   Project? @relation(fields: [projectId], references: [id])
}

model ApiResource {
  id               String   @id @default(cuid())
  sourceUrl        String
  sourceHttpMethod String
  packageName      String
  className        String
  methodName       String
  supplyType       String   @default("HttpApi")
  createdAt        DateTime @default(now())

  projectId String?
  project   Project? @relation(fields: [projectId], references: [id])
}

model SymbolAnalysis {
  id                 String   @id @default(cuid())
  name               String
  kind               String
  path               String?
  detail             Json?
  identifiedConcepts String[] @default([]) // 记录已识别的概念ID
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  projectId String?
  project   Project? @relation(fields: [projectId], references: [id])
}

model GoldenPathConfig {
  id          String   @id @default(cuid())
  name        String
  description String?  @db.Text
  metadata    Json     // 存储项目元数据
  config      Json     // 存储生成的JSON配置
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联到项目（可选）
  projectId String?
  project   Project? @relation(fields: [projectId], references: [id])
  
  // 关联到用户
  userId String?
  user   User?   @relation(fields: [userId], references: [id])
}
