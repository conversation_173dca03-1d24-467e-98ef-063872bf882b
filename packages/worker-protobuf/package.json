{"name": "@autodev/worker-protobuf", "version": "0.6.3", "description": "Model Context Protocol implementation", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.js", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "files": ["dist"], "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "test": "nx jest:test", "lint": "eslint src --ext .ts,.tsx", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "pnpm run build"}, "keywords": ["mcp", "model-context-protocol", "typescript"], "author": "AutoDev authors", "license": "MIT", "dependencies": {"@ast-grep/lang-typescript": "^0.0.2", "@ast-grep/napi": "^0.38.1", "@modelcontextprotocol/sdk": "^1.11.1", "express": "^5.1.0", "protobufjs": "^7.5.1", "zod": "^3.24.4", "@autodev/worker-core": "workspace:*"}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.6", "@types/express": "^5.0.1", "@types/jest": "^29.5.12", "@types/node": "^20.11.24", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "eslint": "^8.57.0", "jest": "^29.7.0", "rollup": "^4.12.0", "rollup-plugin-dts": "^6.1.0", "ts-jest": "^29.1.2", "typescript": "^5.3.3"}, "publishConfig": {"access": "public"}}