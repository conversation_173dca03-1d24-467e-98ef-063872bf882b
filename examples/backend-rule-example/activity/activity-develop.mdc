---
description: 活动开发通用说明，活动开发包括榜单，抽奖，发奖，兑换，任务，签到等模块
globs: 
alwaysApply: false
---

活动开发总结：
**所有活动，涉及发奖，优先扣除用户资产，然后再发放奖励，保证极端情况下，平台不亏损**
一、常用玩法梳理(都有对应的模块实现)
1.1 榜单
通常是在活动中的一些行为，转化为一个分数，统计在榜单上。
1.2 抽奖
活动中获得一些物品（装扮、钻石、礼物等），但有一定的随机性。
1.3 发奖
活动中通过一些行为获得一些奖励。可以是系统自动发的，也可以是用户手动领取的，都是通过 kael 同步发奖即可。
1.4 兑换
用户在一个操作中，先消耗某种物品，再获得某种物品。比如情人节活动用户通过钻石兑换特定礼物。
1.5 任务
指定区间内记录用户的某个行为的进度。
1.6 签到
1.7 系统通知
发站内信
1.8 闯关
房间内任务。
1.9 单个活动特有的玩法
不通用

