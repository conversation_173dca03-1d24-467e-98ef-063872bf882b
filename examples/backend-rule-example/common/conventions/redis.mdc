---
description: Redis 代码规范
globs: 
alwaysApply: false
---

##### Redis
无论使用什么框架，Redis 基本元素类型都采用 String, 例如 redis list 对应 List<String>
```
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.framework.spring.boot.redis.annotation.RedisAutowired;
import org.springframework.stereotype.Service;

@Service
public class RedisServiceExample {

    @RedisAutowired("middleware.redis.service-name")
    private RedisService redisService;
    
    /**
     * 基础键值操作
     */
    public void basicOperations() {
        // 设置键值并指定过期时间(秒)
        redisService.set("key", "value", 3600L);
        
        // 获取键值
        Object value = redisService.get("key");
        
        // 检查键是否存在
        boolean exists = redisService.hasKey("key");
        
        // 删除键
        redisService.del("key");
    }
}
```

##### Redis 锁示例
下面是一个 RedisService.RedisLocker 使用示例：
```java
@RedisAutowired("middleware.redis.service-name")
private RedisService redisService;

/**
 * 使用Redis分布式锁执行操作的简单示例
 */
public void executeWithLock(String resourceId) {
    // 构建锁的key
    String lockKey = "lock:" + resourceId;
    // 创建Redis分布式锁，设置30秒过期时间
    RedisService.RedisLocker redisLocker = redisService.buildLock(lockKey, 30L, TimeUnit.SECONDS);
    
    try {
        // 尝试获取锁
        if (!redisLocker.tryLock()) {
            log.warn("Failed to acquire lock for resource: {}", resourceId);
            return;
        }
        // 获取到锁，执行业务逻辑
        // 业务处理...
        
    } finally {
        // 释放锁
        redisLocker.release();
    }
}
```

这个示例包含了使用 RedisService.RedisLocker 的基本步骤：
1. 构建锁的key
2. 创建锁实例并设置过期时间
3. 尝试获取锁
4. 执行业务逻辑
5. 最后释放锁
